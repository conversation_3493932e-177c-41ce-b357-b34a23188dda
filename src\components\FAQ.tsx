import { Suspense, lazy, useEffect, useRef, useState } from "react";
import { FiPlus } from "react-icons/fi";

interface FAQItemProps {
  question: string;
  answer: string;
  isVisible: boolean;
  index: number;
}

// WorkflowLoop-style FAQ item with DOM-oriented animations
const FAQItem = ({ question, answer, isVisible, index }: FAQItemProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const itemRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const answerRef = useRef<HTMLDivElement>(null);

  // Handle animation when item becomes visible
  useEffect(() => {
    if (isVisible && itemRef.current) {
      requestAnimationFrame(() => {
        if (itemRef.current) {
          itemRef.current.classList.add("is-visible");
        }
      });
    }
  }, [isVisible]);

  // Handle height animation for DOM-oriented approach
  useEffect(() => {
    if (!contentRef.current || !answerRef.current) return;

    if (isOpen) {
      // Get the height of the answer content
      const height = answerRef.current.offsetHeight;
      // Set explicit height for animation
      contentRef.current.style.height = `${height}px`;
    } else {
      // Set height to 0 when closing
      contentRef.current.style.height = "0px";
    }
  }, [isOpen]);

  const toggleAccordion = () => {
    setIsOpen((prevState) => !prevState);
  };

  return (
    <div ref={itemRef} className="faq-item">
      <div
        className="faq-question"
        onClick={toggleAccordion}
        onKeyDown={(e) => e.key === "Enter" && toggleAccordion()}
        role="button"
        tabIndex={0}
        aria-expanded={isOpen}
      >
        <h3 className="text-lg font-medium">{question}</h3>
        <div className={`faq-icon ${isOpen ? "open" : ""}`}>
          <FiPlus size={20} />
        </div>
      </div>
      <div
        ref={contentRef}
        className={`faq-content ${isOpen ? "open" : ""}`}
        style={
          {
            "--index": index,
          } as React.CSSProperties
        }
      >
        <div ref={answerRef} className="faq-answer">
          <p>{answer}</p>
        </div>
      </div>
    </div>
  );
};

// Lazy-loaded FAQ content component
const FAQContent = ({ isVisible }: { isVisible: boolean }) => {
  const [visibleItems, setVisibleItems] = useState<boolean[]>([]);
  const itemsContainerRef = useRef<HTMLDivElement>(null);

  const faqs = [
    {
      question: "What makes your automation services different?",
      answer:
        "While others sell generic automation templates which you have to configure and maintain yourself, Diftra curates tailored, hands-off ecosystems that you'll never have to worry about implementing aside the initial setup procedure. Besides that, hiring in-house is slow, expensive, and they often don't have the skills you need to make great systems up-front. With us, you get a passionate team of world-class automation experts that always work by your side.",
    },
    {
      question: "How quickly can I see results with your automation systems?",
      answer:
        "Our clients see tangible productivity gains within the first week of implementation, ensuring a quick time-to-value. The nature of our ecosystems allows for rapid deployment and integration with your existing tools. The full impact typically becomes evident within 15-20 days as your team fully adapts to the new workflows and efficiencies.",
    },
    {
      question: "Will automation affect the quality of our content?",
      answer:
        "Our automation systems are designed to enhance, not replace, human creativity. They handle routine time-consuming activities and provide AI-assisted content generation, but still incorporate human review and approval. This approach realistically improves performance by ensuring consistency and eliminating common errors and burnout, all on top of freeing your creative team to focus on high-value work instead of mundane tasks. What you're left with is higher quality content at greater scale, resulting in faster ROI and the ability to serve more clients without expanding your team.",
    },
    {
      question: "How customizable are your automation solutions?",
      answer:
        "Our Content Automation Ecosystems are completely customized to your specific needs and workflows; we don't offer one-size-fits-all solutions. Following our thorough discovery process, we're able to understand your unique requirements. This allows us to then design and build a system that integrates with your existing tools and addresses your specific pain points. The solution evolves alongside you, adapting to your needs as they change over time.",
    },
    {
      question: "What kind of ROI can I expect from implementing your systems?",
      answer:
        "Typically, three key areas show the highest ROI: time savings (80-90% reduction in time spent on repetitive tasks), increased capacity (3-4x more content with the same team), and improved profit margins (upwards of 10% increase). The specific ROI for your business will depend on your current processes, the scope of implementation, and how effectively your team adopts the new systems—all factors we evaluate during our discovery process.",
    },
    {
      question: "Do you offer a satisfaction guarantee?",
      answer:
        "Absolutely. We stand by our work with a 100% satisfaction guarantee. We believe that our success is measured by your success, which is why we don't consider a project complete until you're completely satisfied with the results. Our iterative approach includes regular check-ins, feedback sessions, and adjustments to ensure the automation ecosystem we build meets and exceeds your expectations. If at any point you're not completely satisfied, we'll continue revising the solution until it delivers the value you expect.",
    },
  ];

  // Set up intersection observer for animations - optimized version
  useEffect(() => {
    if (!isVisible) return;

    // Initialize visibility array
    setVisibleItems(new Array(faqs.length).fill(false));

    // Use requestIdleCallback for non-critical initialization
    const idleCallback =
      typeof window !== "undefined" && "requestIdleCallback" in window
        ? window.requestIdleCallback
        : (cb: IdleRequestCallback) =>
            setTimeout(
              () => cb({ didTimeout: false, timeRemaining: () => 50 }),
              1,
            );

    idleCallback(() => {
      const observerOptions = {
        root: null,
        rootMargin: "50px",
        threshold: 0.1,
      };

      // Observer for individual items with optimized callback
      const itemObserver = new IntersectionObserver((entries) => {
        // Batch state updates for better performance
        const updates: { index: number; visible: boolean }[] = [];

        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const index = Number.parseInt(
              entry.target.getAttribute("data-index") || "0",
            );
            updates.push({ index, visible: true });
            itemObserver.unobserve(entry.target);
          }
        });

        if (updates.length) {
          setVisibleItems((prev) => {
            const newArray = [...prev];
            updates.forEach((update) => {
              newArray[update.index] = update.visible;
            });
            return newArray;
          });
        }
      }, observerOptions);

      // Observe items with a slight delay to ensure DOM is ready
      setTimeout(() => {
        if (itemsContainerRef.current) {
          const itemElements =
            itemsContainerRef.current.querySelectorAll(".faq-item-wrapper");
          itemElements.forEach((item, index) => {
            item.setAttribute("data-index", index.toString());
            itemObserver.observe(item);
          });
        }
      }, 100);

      return () => {
        itemObserver.disconnect();
      };
    });
  }, [faqs.length, isVisible]);

  return (
    <div className="mx-auto max-w-4xl" ref={itemsContainerRef}>
      {faqs.map((faq, index) => (
        <div
          key={`faq-${index}`}
          className="faq-item-wrapper"
          data-index={index}
        >
          <FAQItem
            question={faq.question}
            answer={faq.answer}
            isVisible={visibleItems[index]}
            index={index}
          />
        </div>
      ))}
    </div>
  );
};

const FAQ = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const [sectionVisible, setSectionVisible] = useState(false);

  // Optimized section visibility detection
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: "100px",
      threshold: 0.1,
    };

    // Observer for the section
    const sectionObserver = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting && sectionRef.current) {
          // Use requestAnimationFrame for smoother animation
          requestAnimationFrame(() => {
            if (sectionRef.current) {
              sectionRef.current.classList.add("is-visible");
              setSectionVisible(true);
              sectionObserver.unobserve(entry.target);
            }
          });
        }
      });
    }, observerOptions);

    // Observe section
    if (sectionRef.current) {
      sectionObserver.observe(sectionRef.current);
    }

    return () => {
      sectionObserver.disconnect();
    };
  }, []);

  return (
    <section
      id="faq"
      className="section-padding bg-gray-950 faq-section"
      ref={sectionRef}
    >
      <div className="container-custom">
        <div className="text-center section-header">
          <h2 className="section-title">
            Frequently Asked <span className="gradient-text">Questions</span>
          </h2>
        </div>

        {/* Lazy load FAQ content only when section becomes visible */}
        {sectionVisible && <FAQContent isVisible={sectionVisible} />}
      </div>
    </section>
  );
};

export default FAQ;
