{"root": ["./src/app.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/components/animatedlogo.tsx", "./src/components/calltoaction.tsx", "./src/components/contactform.tsx", "./src/components/faq.tsx", "./src/components/footer.tsx", "./src/components/header (original).tsx", "./src/components/header.tsx", "./src/components/hero.tsx", "./src/components/privacypolicy.tsx", "./src/components/process.tsx", "./src/components/selfassessment.tsx", "./src/components/services.tsx", "./src/components/termsandconditions.tsx", "./src/components/whychooseus.tsx", "./src/components/whynow.tsx", "./src/hooks/uselogodrawanimation.ts", "./src/hooks/usescrolldirection.ts", "./vite.config.ts"], "version": "5.6.3"}