{"name": "diftra-landing", "private": true, "version": "1.0.0", "type": "module", "description": "Diftra Systems Content Automation Landing Page", "scripts": {"dev": "vite --host 0.0.0.0", "build": "tsc -b && vite build --outDir dist", "lint": "npx biome lint --write && npx tsc --noEmit", "format": "npx biome format --write", "test": "vite test", "preview": "vite preview"}, "dependencies": {"animejs": "^4.0.2", "classnames": "^2.5.1", "framer-motion": "^12.6.5", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-scroll": "^1.9.3", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@eslint/js": "^9.18.0", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.7", "@types/react-scroll": "^1.8.10", "@typescript-eslint/eslint-plugin": "^8.20.0", "@typescript-eslint/parser": "^8.20.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-import-resolver-typescript": "^3.7.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "postcss": "^8.5.1", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.10", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "typescript-eslint": "^8.20.0", "vite": "^6.0.5", "vite-plugin-svgr": "^4.3.0"}}