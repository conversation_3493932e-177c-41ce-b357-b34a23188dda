import { motion } from "framer-motion";

const WhyNow = () => {
  return (
    <section id="why-now" className="section-padding bg-gray-950">
      <div className="container-custom">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="max-w-3xl mx-auto text-center"
        >
          <h2 className="section-title mb-8">
            Why Look at {" "} Automation <span className="gradient-text">Now?</span>
          </h2>
          <p className="text-lg text-gray-300 mb-12 max-w-2xl mx-auto">
            With rising demands and shrinking timelines, intelligent systems are
            quietly changing how the global increase in workload is being handled
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-left">
            <div className="bg-gray-800/40 px-8 py-5 rounded-lg card-hover-effect transform-gpu">
              <div className="font-bold text-purple-400 mb-3 text-lg">
                Industry Movement
              </div>
              <div className="text-gray-400 text-base leading-relaxed">
                Over half of digital studios have adopted some form of content
                automation, freeing up their teams to focus on relationships and
                creative work.
              </div>
            </div>
            <div className="bg-gray-800/40 px-8 py-5 rounded-lg card-hover-effect transform-gpu">
              <div className="font-bold text-purple-400 mb-3 text-lg">
                Evolving Client Expectations
              </div>
              <div className="text-gray-400 text-base leading-relaxed">
                Clients now expect faster turnarounds and more sophisticated
                multi-channel campaigns—often without an increase in budget.
              </div>
            </div>
            <div className="bg-gray-800/40 px-8 py-5 rounded-lg card-hover-effect transform-gpu">
              <div className="font-bold text-purple-400 mb-3 text-lg">
                Adaptation Over Disruption
              </div>
              <div className="text-gray-400 text-base leading-relaxed">
                Exploring automation doesn’t have to mean overnight change. Many
                businesses start small and grow their systems over time,
                responding to what works.
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default WhyNow;
