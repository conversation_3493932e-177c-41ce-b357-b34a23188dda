import { motion } from "framer-motion";

const WhyNow = () => {
  return (
    <section id="why-now" className="section-padding bg-gray-950">
      <div className="container-custom">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="max-w-3xl mx-auto text-center"
        >
          <h2 className="section-title mb-8">
            Why Look at {" "} Automation <span className="gradient-text">Now?</span>
          </h2>
          <p className="text-lg text-gray-300 mb-12 max-w-2xl mx-auto">
            With rising demands and shrinking timelines, intelligent systems are
            quietly changing how the global increase in workload is being handled
          </p>
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8 text-left max-w-6xl mx-auto">
            <div className="bg-gray-800/40 p-6 rounded-lg card-hover-effect transform-gpu">
              <div className="font-bold text-purple-400 mb-4 text-lg">
                Industry Progression
              </div>
              <div className="text-gray-400 text-base leading-relaxed">
                Automation tools have reached a stable maturity level. What used to take
                years to implement can now be deployed within weeks.
              </div>
            </div>
            <div className="bg-gray-800/40 p-6 rounded-lg card-hover-effect transform-gpu">
              <div className="font-bold text-purple-400 mb-4 text-lg">
                Evolving Client Expectations
              </div>
              <div className="text-gray-400 text-base leading-relaxed">
                Clients now expect faster turnarounds and more sophisticated
                multi-channel campaigns, often without an increase in budget.
              </div>
            </div>
            <div className="bg-gray-800/40 p-6 rounded-lg card-hover-effect transform-gpu">
              <div className="font-bold text-purple-400 mb-4 text-lg">
                Early Bird Advantage
              </div>
              <div className="text-gray-400 text-base leading-relaxed">
                While competitors hesitate, forward-thinking businesses are capturing
                market share through quicker delivery and lower costs enabled by automation.
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default WhyNow;
