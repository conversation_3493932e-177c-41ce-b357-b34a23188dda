import { motion } from "framer-motion";

const Process = () => {
  const steps = [
    {
      number: "01",
      title: "Discovery & Assessment",
      description:
        "We start by understanding your unique needs, challenges, and goals. Through in-depth analysis, we identify the key areas where automation can have the biggest impact.",
    },
    {
      number: "02",
      title: "Strategic Blueprint",
      description:
        "Based on our findings, we create a comprehensive blueprint for your Content Automation Ecosystem, detailing the architecture, tools, and integration points.",
    },
    {
      number: "03",
      title: "Custom Implementation",
      description:
        "Our team builds your ecosystem, incorporating best-in-class instruments designed specifically for your workflow and requirements.",
    },
    {
      number: "04",
      title: "Team Training & Onboarding",
      description:
        "We ensure your team is fully trained on the new systems with hands-on support during the transition.",
    },
    {
      number: "05",
      title: "Ongoing Optimization",
      description:
        "We continuously monitor and refine your automation ecosystem, making adjustments and improvements based on performance data and your evolving needs.",
    },
  ];

  return (
    <section id="process" className="section-padding bg-gray-900">
      <div className="container-custom">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center"
        >
          <h2 className="section-title">
            Our <span className="gradient-text">Process</span>
          </h2>
          <p className="section-subtitle">
          </p>
        </motion.div>

        <div className="relative mt-16">
          {/* Vertical line to connect the process steps */}
          <div className="absolute left-0 top-0 hidden h-full w-1 bg-gray-800 lg:left-1/2 lg:block lg:-ml-0.5"></div>

          {steps.map((step, index) => (
            <motion.div
              key={`step-${step.number}`}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className={`relative mb-12 flex flex-col lg:flex-row ${
                index % 2 === 0 ? "lg:flex-row-reverse" : ""
              }`}
            >
              {/* Step number for mobile */}
              <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-purple-700/20 text-lg font-bold text-purple-400 lg:hidden">
                {step.number}
              </div>

              {/* Content box */}
              <div
                className={`lg:w-1/2 ${index % 2 === 0 ? "lg:pl-16" : "lg:pr-16"}`}
              >
                <div className="rounded-lg border border-gray-800 bg-gray-800/30 p-6 backdrop-blur-sm card-hover-effect">
                  <h3 className="mb-4 flex items-center text-xl font-bold">
                    <span className="mr-3 hidden h-12 w-12 items-center justify-center rounded-full bg-purple-700/20 text-lg font-bold text-purple-400 lg:flex">
                      {step.number}
                    </span>
                    {step.title}
                  </h3>
                  <p className="text-gray-400">{step.description}</p>
                </div>
              </div>

              {/* This creates the space for the empty half on desktop */}
              <div className="hidden lg:block lg:w-1/2"></div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Process;
