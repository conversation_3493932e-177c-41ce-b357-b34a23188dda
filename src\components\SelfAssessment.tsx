import { AnimatePresence, motion } from "framer-motion";
import { useRef, useState } from "react";
import { FiChevronDown } from "react-icons/fi";

const QUESTIONS = [
  {
    text: "How much of your content planning is automated versus manual?",
    min: "Completely manual",
    max: "Fully automated",
  },
  {
    text: "Do you use AI or scripts to generate or repurpose content?",
    min: "Never",
    max: "For all content",
  },
  {
    text: "How are approvals and revisions handled?",
    min: "All manual",
    max: "Fully automated",
  },
  {
    text: "How integrated is your content workflow across tools and platforms?",
    min: "Separate systems",
    max: "Fully integrated",
  },
  {
    text: "How do you distribute content to channels?",
    min: "Each manually",
    max: "Unified system",
  },
  {
    text: "Adaptability to new clients/platforms?",
    min: (
      <>
        Requires <br /> complete rework
      </>
    ),
    max: (
      <>
        Seamless <br /> integration
      </>
    ),
  },
  {
    text: "How much time do you spend on repetitive content tasks?",
    min: "Most of our time",
    max: "Minimal time",
  },
];

function calcResult(scores: number[]) {
  // Round the average to the nearest 0.1 for a smoother experience
  const avg =
    Math.round((scores.reduce((a, b) => a + b, 0) / scores.length) * 10) / 10;

  if (avg < 1) {
    return {
      title: "Early Exploration Stage",
      advice:
        "You're just beginning your automation journey. Start by identifying one repetitive content task that consumes significant time and explore simple automation tools to address it. Even small improvements can yield noticeable time savings and demonstrate the value of automation to your team.",
    };
  } else if (avg < 2) {
    return {
      title: "Foundation Building Stage",
      advice:
        "You've taken the first steps toward automation. Now is the time to formalize your approach by documenting your content workflows and identifying bottlenecks. Consider implementing template systems and basic scheduling tools to create a foundation for more advanced automation.",
    };
  } else if (avg < 3) {
    return {
      title: "Growing Automation Awareness",
      advice:
        "You're using several automation tools but may lack a cohesive strategy. Focus on connecting your existing tools and standardizing processes across clients. This is an excellent time to explore AI-assisted content generation for first drafts while maintaining your unique voice and quality standards.",
    };
  } else if (avg < 4) {
    return {
      title: "Strategic Implementation Phase",
      advice:
        "You have a solid automation foundation in place. Your next steps should focus on creating a unified ecosystem where your tools work together seamlessly. Consider implementing workflow automation that connects your content planning, creation, approval, and distribution processes.",
    };
  } else if (avg < 5) {
    return {
      title: "Advanced Automation Ecosystem",
      advice:
        "Your agency has embraced automation across most content processes. To reach the next level, explore predictive content planning using performance data, implement dynamic content personalization, and develop custom integrations that perfectly match your unique workflow needs.",
    };
  } else {
    return {
      title: "Automation Excellence",
      advice:
        "You're operating at the cutting edge of content automation. Your focus now should be on continuous refinement, exploring emerging AI technologies, and developing proprietary systems that give you a competitive advantage. Consider how you can use your automation expertise as a selling point to attract larger clients.",
    };
  }
}

const SelfAssessment = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [responses, setResponses] = useState<number[]>(
    Array(QUESTIONS.length).fill(0),
  );
  const [submitted, setSubmitted] = useState(false);
  // Add state to track animation direction
  const [animationDirection, setAnimationDirection] = useState<"formToResults" | "resultsToForm" | null>(null);
  const assessmentRef = useRef<HTMLDivElement>(null);

  // Define separate transition objects for each animation phase
  // This gives you complete control over all 4 animation phases:
  // 1. Form appearing (when clicking "Retake Assessment")
  // 2. Form disappearing (when clicking "See My Insights")
  // 3. Results appearing (when clicking "See My Insights")
  // 4. Results disappearing (when clicking "Retake Assessment")
  //
  // IMPORTANT: Each animation phase uses its own independent transition settings
  // The enter and exit animations are completely separate, so changing the delay
  // for one phase will not affect the other phases
  
  // Form animations
  const formEnterTransition = {
    duration: 0.3,  // Duration in seconds for form appearing
    delay: 0.,     // Delay before form appears
    ease: [0.4, 0.0, 0.2, 1]  // Easing function for form appearing
  };
  
  const formExitTransition = {
    duration: 0.4,  // Duration in seconds for form disappearing
    delay: 0.,     // Delay before form disappears
    ease: [0.4, 0.0, 0.2, 1]  // Easing function for form disappearing
  };
  
  // Results animations
  const resultsEnterTransition = {
    duration: 0.3,  // Duration in seconds for results appearing
    delay: 0.4,     // Delay before results appear
    ease: [0.4, 0.0, 0.2, 1]  // Easing function for results appearing
  };
  
  const resultsExitTransition = {
    duration: 0.3,  // Duration in seconds for results disappearing
    delay: 0.0,     // Delay before results disappear
    ease: [0.4, 0.0, 0.2, 1]  // Easing function for results disappearing
  };
  
  // NOTE: You can adjust these values independently to get exactly the animation feel you want
  // For example, you might want:
  // - Results to appear slowly with a dramatic fade (longer duration, longer delay)
  // - Results to disappear quickly (shorter duration, minimal delay)
  // - Form to reappear quickly after results disappear (shorter duration, minimal delay)
  // - Form to disappear with a quick fade when submitting (shorter duration)
  //
  // This gives you complete creative control over the animation sequence and timing

  const handleSlider = (idx: number, val: number) => {
    setResponses((prev) => {
      const next = [...prev];
      next[idx] = val;
      return next;
    });
  };

  const smoothScrollTo = (element: HTMLElement, offset = 100) => {
    const elementRect = element.getBoundingClientRect();
    const targetPosition = window.pageYOffset + elementRect.top - offset;
    const startPosition = window.pageYOffset;
    const distance = targetPosition - startPosition;
    const duration = 800;
    let startTime: number | null = null;

    const easeInOutCubic = (t: number): number => {
      return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    };

    const animation = (currentTime: number) => {
      if (startTime === null) startTime = currentTime;
      const timeElapsed = currentTime - startTime;
      const progress = Math.min(timeElapsed / duration, 1);
      const ease = easeInOutCubic(progress);

      window.scrollTo(0, startPosition + distance * ease);

      if (progress < 1) {
        requestAnimationFrame(animation);
      }
    };

    requestAnimationFrame(animation);
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setSubmitted(true);
    // Set animation direction to formToResults when showing results
    setAnimationDirection("formToResults");

    // Smooth scroll to results after animation - coordinated with transition timing
    // We wait for the form to exit (formExitTransition.duration) and then for the results to start appearing
    // (resultsEnterTransition.delay) before scrolling to ensure a smooth visual experience
    setTimeout(() => {
      if (assessmentRef.current) {
        smoothScrollTo(assessmentRef.current, 100);
      }
    }, formExitTransition.duration * 1000 + resultsEnterTransition.delay * 1000);
  };

  const handleRetake = () => {
    setSubmitted(false);
    setResponses(Array(QUESTIONS.length).fill(0));
    // Set animation direction to resultsToForm when going back to the form
    setAnimationDirection("resultsToForm");

    // Smooth scroll back to form during transition
    // We wait for the results to exit (resultsExitTransition.duration) and then for the form to start appearing
    // (formEnterTransition.delay) before scrolling to ensure a smooth visual experience
    setTimeout(() => {
      if (assessmentRef.current) {
        smoothScrollTo(assessmentRef.current, 80);
      }
    }, resultsExitTransition.duration * 1000 + formEnterTransition.delay * 1000);
  };

  const result = calcResult(responses);

  return (
    <motion.section
      layout
      transition={{ duration: 0.6, ease: [0.4, 0.0, 0.2, 1] }}
      className="section-padding bg-gray-950 relative"
    >
      {/* Decorative U-shaped Frame */}
      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-full max-w-3xl mx-auto pointer-events-none">
        {/* Top horizontal line */}
        <div className="h-[2px] bg-gradient-to-r from-purple-400 to-pink-600"></div>

        {/* Container for vertical lines */}
        <div className="relative">
          {/* Left vertical line */}
          <div className="absolute left-0 top-0 w-[2px] h-[250px] bg-gradient-to-b from-purple-400 via-purple-400/50 to-transparent"></div>

          {/* Right vertical line */}
          <div className="absolute right-0 top-0 w-[2px] h-[250px] bg-gradient-to-b from-pink-600 via-pink-600/50 to-transparent"></div>
        </div>
      </div>

      <div className="container-custom">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="mx-auto max-w-2xl text-center"
        >
          <h2 className="section-title mb-3">
            Content Automation Self-Assessment
          </h2>
          <p className="text-lg text-gray-300 mb-12 max-w-2xl mx-auto">
            Gauge where your business stands on the automation journey and
            discover your next steps.
          </p>

          {/* ANIMATION 1: Main "Take Assessment" Dropdown Button */}
          <motion.button
            onClick={() => {
              const newIsOpen = !isOpen;
              setIsOpen(newIsOpen);

              // Smooth scroll to assessment when opening
              if (newIsOpen) {
                setTimeout(() => {
                  if (assessmentRef.current) {
                    smoothScrollTo(assessmentRef.current, 80);
                  }
                }, 500);
              }
            }}
            whileHover={{
              scale: 1.02,
              boxShadow: "0 10px 25px rgba(147, 51, 234, 0.3)",
              transition: { duration: 0.15 }
            }}
            whileTap={{ scale: 0.98 }}
            className="w-full bg-purple-600 rounded-md px-6 py-4 text-center text-lg font-semibold text-white shadow-lg transition duration-200 hover:bg-purple-700 flex items-center justify-center gap-2"
            aria-expanded={isOpen}
          >
            <span>Take Self-Assessment</span>
            <motion.div
              animate={{ rotate: isOpen ? 180 : 0 }}
              transition={{ duration: 0.4, ease: [0.4, 0.0, 0.2, 1] }}
            >
              <FiChevronDown className="h-5 w-5" />
            </motion.div>
          </motion.button>

          {/* Dropdown Content */}
          <AnimatePresence>
            {isOpen && (
              <motion.div
                key="assessment-content"
                ref={assessmentRef} // Keep the ref here, it's used in multiple places
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: "auto", opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.4, ease: "easeInOut" }}
                className="overflow-hidden"
                style={{ minHeight: submitted ? "auto" : "auto" }}
              >
                <motion.div
                  layout
                  transition={{
                    duration: 0.6,
                    ease: [0.4, 0.0, 0.2, 1],
                    layout: { duration: 0.6 }
                  }} // Define layout transition
                  className="mt-4 rounded-2xl border border-gray-800 bg-gray-900/70 p-8 text-center backdrop-blur-sm"
                >
                  <div className="relative">
                    <AnimatePresence mode="popLayout">
                    {!submitted ? (
                      <motion.div
                        key="form"
                        layoutRoot
                        initial={{ opacity: 0 }}
                        animate={{
                          opacity: 1,
                          transition: animationDirection === "resultsToForm" ? formEnterTransition : {}
                        }}
                        exit={{
                          opacity: 0,
                          transition: animationDirection === "formToResults" ? formExitTransition : {}
                        }}
                        className="w-full"
                      >
                        <p className="mb-8 text-gray-400">
                          Use the sliders below to reflect your agency's current
                          process.
                          <br />
                          No sales pitch—just honest, practical feedback.
                        </p>
                        <form
                          onSubmit={handleSubmit}
                          className="flex flex-col gap-6"
                          style={{
                            width: "100%",
                            maxWidth: 600,
                            margin: "0 auto",
                          }}
                        >
                          {QUESTIONS.map((q, idx) => (
                            <motion.div
                              key={q.text}
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{
                                delay: 0.1 + idx * 0.1,
                                duration: 0.4,
                                ease: [0.4, 0.0, 0.2, 1]
                              }}
                              className="flex flex-col items-stretch gap-2 p-3"
                            >
                              <label className="text-left font-medium text-white mb-0.5 text-base">
                                {q.text}
                              </label>
                              <div className="grid grid-cols-[1fr_auto_1fr] md:grid-cols-[1fr_240px_1fr] items-center w-full">
                                <div className="pr-2 md:pr-4 text-right">
                                  <span className="text-xs md:text-sm text-gray-400">
                                    {q.min}
                                  </span>
                                </div>
                                <div className="flex justify-center w-[140px] md:w-auto">
                                  <input
                                    type="range"
                                    min={0}
                                    max={5}
                                    step="any"
                                    value={responses[idx]}
                                    onChange={(
                                      e: React.ChangeEvent<HTMLInputElement>,
                                    ) => {
                                      const value = Number.parseFloat(
                                        e.target.value,
                                      );
                                      handleSlider(idx, value);
                                    }}
                                    className="w-full accent-purple-500 slider-thumb h-2"
                                    style={{ accentColor: "#a78bfa" }}
                                  />
                                </div>
                                <div className="pl-2 md:pl-4 text-left">
                                  <span className="text-xs md:text-sm text-gray-400">
                                    {q.max}
                                  </span>
                                </div>
                              </div>
                            </motion.div>
                          ))}
                          {/* ANIMATION 2: "See My Insights" button - triggers formToResults transition */}
                          <motion.button
                            type="submit"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{
                              delay: 0.1 + QUESTIONS.length * 0.1 + 0.2,
                              duration: 0.4,
                              ease: [0.4, 0.0, 0.2, 1]
                            }}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            className="btn-primary text-base px-8 py-2 mt-2"
                          >
                            See My Insights
                          </motion.button>
                        </form>
                      </motion.div>
                    ) : (
                      <motion.div
                        key="results"
                        layoutRoot
                        initial={{ opacity: 0 }}
                        animate={{
                          opacity: 1,
                          transition: animationDirection === "formToResults" ? resultsEnterTransition : {}
                        }}
                        exit={{
                          opacity: 0,
                          transition: animationDirection === "resultsToForm" ? resultsExitTransition : {}
                        }}
                        className="w-full mt-8 rounded-lg border border-purple-700 bg-purple-900/30 p-8 shadow-lg"
                      >
                        <div className="text-lg font-bold text-purple-300 mb-2">
                          {result.title}
                        </div>
                        <div className="text-base text-gray-100 mb-6">
                          {result.advice}
                        </div>
                        {/* ANIMATION 3: "Retake Assessment" button - triggers resultsToForm transition */}
                        <button
                          className="btn-secondary px-6 py-2"
                          onClick={handleRetake}
                        >
                          Retake Assessment
                        </button>
                      </motion.div>
                    )}
                    </AnimatePresence>
                  </div>
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </div>
    </motion.section>
  ); // Correct indentation and closing tag
};

export default SelfAssessment;
